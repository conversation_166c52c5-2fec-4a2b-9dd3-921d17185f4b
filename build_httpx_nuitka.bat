@echo off
REM 将 httpx 编译为 Nuitka 中间包的批处理脚本
REM 适用于 Windows 环境

setlocal enabledelayedexpansion

echo ========================================
echo httpx Nuitka 编译脚本
echo ========================================

REM 检查 Python 是否可用
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: Python 未安装或不在 PATH 中
    echo 请确保 Python 已正确安装并添加到系统 PATH
    pause
    exit /b 1
)

REM 获取 Python 版本信息
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo 使用 Python 版本: %PYTHON_VERSION%

REM 设置默认输出目录
set OUTPUT_DIR=httpx_nuitka_build
if not "%~1"=="" set OUTPUT_DIR=%~1

echo 输出目录: %OUTPUT_DIR%
echo.

REM 检查并安装必要的依赖项
echo 检查依赖项...
python -c "import nuitka" >nul 2>&1
if errorlevel 1 (
    echo Nuitka 未安装，正在安装...
    python -m pip install nuitka
    if errorlevel 1 (
        echo 错误: 无法安装 Nuitka
        pause
        exit /b 1
    )
)

python -c "import httpx" >nul 2>&1
if errorlevel 1 (
    echo httpx 未安装，正在安装...
    python -m pip install httpx[http2]
    if errorlevel 1 (
        echo 错误: 无法安装 httpx
        pause
        exit /b 1
    )
)

REM 运行编译脚本
echo.
echo 开始编译...
python build_httpx_nuitka.py --output-dir "%OUTPUT_DIR%"

if errorlevel 1 (
    echo.
    echo 编译失败!
    pause
    exit /b 1
) else (
    echo.
    echo 编译成功完成!
    echo 编译结果保存在: %OUTPUT_DIR%
    echo.
    echo 您现在可以在 Nuitka 项目中使用编译后的 httpx 模块
    pause
)

endlocal
