#!/usr/bin/env python3
"""
将 httpx 编译为 Nuitka 中间包的脚本
用于优化 httpx 及其依赖项的性能和减少分发大小
"""

import os
import sys
import subprocess
import shutil
import tempfile
import json
import platform
from pathlib import Path
from typing import List, Optional, Dict, Any

# httpx 及其主要依赖项列表
HTTPX_DEPENDENCIES = [
    'httpx',
    'httpcore', 
    'h11',
    'h2',
    'hpack',
    'hyperframe',
    'certifi',
    'charset-normalizer',
    'idna',
    'sniffio',
    'anyio',
    'rfc3986',
    'click',  # 如果使用 httpx 命令行工具
]

# Nuitka 编译选项
NUITKA_OPTIONS = [
    '--module',
    '--include-package=httpx',
    '--include-package=httpcore',
    '--include-package=h11',
    '--include-package=h2',
    '--include-package=certifi',
    '--include-package=charset_normalizer',
    '--include-package=idna',
    '--include-package=sniffio',
    '--include-package=anyio',
    '--follow-imports',
    '--assume-yes-for-downloads',
    '--show-progress',
    '--show-memory',
    '--python-flag=no_site',
    '--python-flag=no_warnings',
    '--enable-plugin=anti-bloat',
    '--nofollow-import-to=pytest',
    '--nofollow-import-to=unittest',
    '--nofollow-import-to=doctest',
    '--optimization=2',
]

class HttpxNuitkaBuilder:
    """httpx Nuitka 编译器"""

    def __init__(self, output_dir: str = "httpx_nuitka_build", config_file: Optional[str] = None):
        self.output_dir = Path(output_dir).resolve()
        self.temp_dir = None
        self.python_executable = sys.executable
        self.config = self.load_config(config_file)

    def load_config(self, config_file: Optional[str] = None) -> Dict[str, Any]:
        """加载配置文件"""
        default_config_file = Path("nuitka_httpx_config.json")

        if config_file:
            config_path = Path(config_file)
        elif default_config_file.exists():
            config_path = default_config_file
        else:
            # 返回默认配置
            return {
                "dependencies": {"required": HTTPX_DEPENDENCIES},
                "nuitka_options": {"basic": NUITKA_OPTIONS},
                "build_profiles": {"standard": {"includes": HTTPX_DEPENDENCIES}},
                "output_settings": {"module_name": "httpx_nuitka_module"}
            }

        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                print(f"✅ 已加载配置文件: {config_path}")
                return config
        except Exception as e:
            print(f"⚠️ 无法加载配置文件 {config_path}: {e}")
            print("使用默认配置...")
            return {
                "dependencies": {"required": HTTPX_DEPENDENCIES},
                "nuitka_options": {"basic": NUITKA_OPTIONS},
                "build_profiles": {"standard": {"includes": HTTPX_DEPENDENCIES}},
                "output_settings": {"module_name": "httpx_nuitka_module"}
            }

    def get_nuitka_options(self, profile: str = "standard") -> List[str]:
        """获取 Nuitka 编译选项"""
        options = []

        # 基础选项
        if "nuitka_options" in self.config:
            nuitka_config = self.config["nuitka_options"]
            for category in ["basic", "optimization", "includes", "excludes"]:
                if category in nuitka_config:
                    options.extend(nuitka_config[category])
        else:
            options = NUITKA_OPTIONS.copy()

        # 平台特定选项
        current_platform = platform.system().lower()
        if "platform_specific" in self.config and current_platform in self.config["platform_specific"]:
            platform_config = self.config["platform_specific"][current_platform]
            if "additional_options" in platform_config:
                options.extend(platform_config["additional_options"])

        return options
        
    def check_dependencies(self) -> bool:
        """检查必要的依赖项"""
        print("检查依赖项...")
        
        # 检查 Nuitka
        try:
            result = subprocess.run([self.python_executable, '-m', 'nuitka', '--version'], 
                                  capture_output=True, text=True)
            if result.returncode != 0:
                print("❌ Nuitka 未安装或无法运行")
                print("请运行: pip install nuitka")
                return False
            print(f"✅ Nuitka 版本: {result.stdout.strip()}")
        except Exception as e:
            print(f"❌ 检查 Nuitka 时出错: {e}")
            return False
            
        # 检查 httpx 及其依赖项
        missing_packages = []
        for package in HTTPX_DEPENDENCIES:
            try:
                __import__(package.replace('-', '_'))
                print(f"✅ {package} 已安装")
            except ImportError:
                missing_packages.append(package)
                print(f"❌ {package} 未安装")
                
        if missing_packages:
            print(f"\n请安装缺失的包: pip install {' '.join(missing_packages)}")
            return False
            
        return True
    
    def create_httpx_module(self) -> Path:
        """创建 httpx 模块入口文件"""
        module_content = '''
"""
httpx Nuitka 编译模块
这个模块重新导出 httpx 的所有公共接口
"""

# 导入 httpx 的主要组件
from httpx import *
from httpx import (
    # 主要类
    Client, AsyncClient, Response, Request,
    # 异常类
    HTTPError, RequestError, HTTPStatusError, 
    ConnectError, ReadTimeout, WriteTimeout, PoolTimeout,
    # 配置类
    Limits, Timeout, Proxy, Auth,
    # 工具函数
    get, post, put, patch, delete, head, options,
    # 版本信息
    __version__,
)

# 导入 httpcore 的核心组件（如果需要）
try:
    import httpcore
    __all_httpcore__ = httpcore.__all__ if hasattr(httpcore, '__all__') else []
except ImportError:
    __all_httpcore__ = []

# 导入其他相关模块
try:
    import h11
    import h2
    import certifi
    import charset_normalizer
    import idna
    import sniffio
    import anyio
except ImportError as e:
    print(f"Warning: Could not import optional dependency: {e}")

# 定义公共接口
__all__ = [
    # 主要类
    'Client', 'AsyncClient', 'Response', 'Request',
    # 异常类
    'HTTPError', 'RequestError', 'HTTPStatusError',
    'ConnectError', 'ReadTimeout', 'WriteTimeout', 'PoolTimeout',
    # 配置类
    'Limits', 'Timeout', 'Proxy', 'Auth',
    # 工具函数
    'get', 'post', 'put', 'patch', 'delete', 'head', 'options',
    # 版本信息
    '__version__',
]

def get_version():
    """获取 httpx 版本"""
    return __version__

def create_client(**kwargs):
    """创建 httpx 客户端的便捷函数"""
    return Client(**kwargs)

def create_async_client(**kwargs):
    """创建 httpx 异步客户端的便捷函数"""
    return AsyncClient(**kwargs)
'''
        
        module_file = self.temp_dir / "httpx_nuitka_module.py"
        module_file.write_text(module_content.strip(), encoding='utf-8')
        return module_file
    
    def build_with_nuitka(self, module_file: Path, profile: str = "standard") -> bool:
        """使用 Nuitka 编译模块"""
        print(f"\n开始编译 {module_file.name}...")
        print(f"使用编译配置: {profile}")

        # 获取 Nuitka 选项
        nuitka_options = self.get_nuitka_options(profile)

        # 构建 Nuitka 命令
        cmd = [self.python_executable, '-m', 'nuitka'] + nuitka_options + [
            f'--output-dir={self.output_dir}',
            str(module_file)
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        
        try:
            # 运行 Nuitka 编译
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                cwd=self.temp_dir
            )
            
            # 实时输出编译进度
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    print(output.strip())
            
            return_code = process.poll()
            
            if return_code == 0:
                print("✅ 编译成功!")
                return True
            else:
                print(f"❌ 编译失败，返回码: {return_code}")
                return False
                
        except Exception as e:
            print(f"❌ 编译过程中出错: {e}")
            return False
    
    def create_usage_example(self):
        """创建使用示例"""
        example_content = '''
"""
httpx Nuitka 编译包使用示例
"""

# 导入编译后的 httpx 模块
import httpx_nuitka_module as httpx

def test_sync_client():
    """测试同步客户端"""
    print("测试同步客户端...")
    
    with httpx.Client() as client:
        response = client.get('https://httpbin.org/get')
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")

async def test_async_client():
    """测试异步客户端"""
    print("测试异步客户端...")
    
    async with httpx.AsyncClient() as client:
        response = await client.get('https://httpbin.org/get')
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")

def test_convenience_functions():
    """测试便捷函数"""
    print("测试便捷函数...")
    
    response = httpx.get('https://httpbin.org/get')
    print(f"GET 请求状态码: {response.status_code}")
    
    response = httpx.post('https://httpbin.org/post', json={'key': 'value'})
    print(f"POST 请求状态码: {response.status_code}")

if __name__ == '__main__':
    import asyncio
    
    print(f"httpx 版本: {httpx.get_version()}")
    
    # 测试同步功能
    test_sync_client()
    test_convenience_functions()
    
    # 测试异步功能
    asyncio.run(test_async_client())
    
    print("所有测试完成!")
'''
        
        example_file = self.output_dir / "httpx_usage_example.py"
        example_file.write_text(example_content.strip(), encoding='utf-8')
        print(f"✅ 使用示例已保存到: {example_file}")
    
    def build(self) -> bool:
        """执行完整的编译流程"""
        print("🚀 开始编译 httpx 为 Nuitka 中间包...")
        
        # 检查依赖项
        if not self.check_dependencies():
            return False
        
        # 创建临时目录
        self.temp_dir = Path(tempfile.mkdtemp(prefix="httpx_nuitka_"))
        print(f"临时目录: {self.temp_dir}")
        
        try:
            # 创建输出目录
            self.output_dir.mkdir(parents=True, exist_ok=True)
            
            # 创建 httpx 模块文件
            module_file = self.create_httpx_module()
            
            # 使用 Nuitka 编译
            if not self.build_with_nuitka(module_file):
                return False
            
            # 创建使用示例
            self.create_usage_example()
            
            print(f"\n🎉 编译完成!")
            print(f"输出目录: {self.output_dir}")
            print(f"编译后的文件可以在您的 Nuitka 项目中使用")
            
            return True
            
        finally:
            # 清理临时目录
            if self.temp_dir and self.temp_dir.exists():
                shutil.rmtree(self.temp_dir)
                print(f"已清理临时目录: {self.temp_dir}")

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="将 httpx 编译为 Nuitka 中间包")
    parser.add_argument(
        '--output-dir', '-o',
        default='httpx_nuitka_build',
        help='输出目录 (默认: httpx_nuitka_build)'
    )
    parser.add_argument(
        '--config', '-c',
        help='配置文件路径 (默认: nuitka_httpx_config.json)'
    )
    parser.add_argument(
        '--profile', '-p',
        default='standard',
        choices=['minimal', 'standard', 'full'],
        help='编译配置文件 (默认: standard)'
    )
    parser.add_argument(
        '--python',
        default=sys.executable,
        help='Python 解释器路径'
    )

    args = parser.parse_args()

    # 创建编译器实例
    builder = HttpxNuitkaBuilder(args.output_dir, args.config)
    builder.python_executable = args.python
    
    # 执行编译
    success = builder.build()
    
    if success:
        print("\n✅ 编译成功完成!")
        sys.exit(0)
    else:
        print("\n❌ 编译失败!")
        sys.exit(1)

if __name__ == '__main__':
    main()
