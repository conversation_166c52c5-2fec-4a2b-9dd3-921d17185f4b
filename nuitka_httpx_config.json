{"description": "httpx Nuitka 编译配置文件", "version": "1.0.0", "dependencies": {"required": ["httpx", "httpcore", "h11", "h2", "hpack", "hyperframe", "certifi", "charset-normalizer", "idna", "sniffio", "anyio", "rfc3986"], "optional": ["click", "trio", "curio"]}, "nuitka_options": {"basic": ["--module", "--follow-imports", "--assume-yes-for-downloads", "--show-progress", "--show-memory"], "optimization": ["--optimization=2", "--python-flag=no_site", "--python-flag=no_warnings", "--enable-plugin=anti-bloat"], "includes": ["--include-package=httpx", "--include-package=httpcore", "--include-package=h11", "--include-package=h2", "--include-package=certifi", "--include-package=charset_normalizer", "--include-package=idna", "--include-package=sniffio", "--include-package=anyio"], "excludes": ["--nofollow-import-to=pytest", "--nofollow-import-to=unittest", "--nofollow-import-to=doctest", "--nofollow-import-to=tkinter", "--nofollow-import-to=matplotlib", "--nofollow-import-to=numpy", "--nofollow-import-to=pandas"]}, "build_profiles": {"minimal": {"description": "最小化编译，只包含核心功能", "includes": ["httpx", "httpcore", "h11", "certifi"], "excludes": ["h2", "hpack", "hyperframe"]}, "standard": {"description": "标准编译，包含 HTTP/1.1 和 HTTP/2 支持", "includes": ["httpx", "httpcore", "h11", "h2", "certifi", "charset_normalizer"], "excludes": []}, "full": {"description": "完整编译，包含所有功能和可选依赖", "includes": ["httpx", "httpcore", "h11", "h2", "certifi", "charset_normalizer", "click"], "excludes": []}}, "platform_specific": {"windows": {"additional_options": ["--mingw64"], "dll_excludes": ["api-ms-win-*.dll", "vcruntime*.dll"]}, "linux": {"additional_options": ["--linux-onefile-icon=httpx.png"], "so_excludes": []}, "macos": {"additional_options": ["--macos-app-icon=httpx.icns"], "dylib_excludes": []}}, "output_settings": {"module_name": "httpx_nuitka_module", "output_dir": "httpx_nuitka_build", "create_examples": true, "create_tests": true, "create_documentation": true}, "advanced_options": {"memory_optimization": true, "size_optimization": true, "speed_optimization": true, "debug_mode": false, "trace_execution": false, "profile_compilation": false}, "custom_patches": {"description": "自定义补丁和修改", "httpx_patches": [{"name": "disable_http3", "description": "禁用 HTTP/3 支持以减少依赖", "enabled": false}, {"name": "optimize_ssl_context", "description": "优化 SSL 上下文创建", "enabled": true}]}, "testing": {"run_tests_after_build": true, "test_endpoints": ["https://httpbin.org/get", "https://httpbin.org/post", "https://httpbin.org/status/200"], "timeout_seconds": 30}}