#!/usr/bin/env python3
"""
最小化构建脚本，专门用于测试 httpx 问题
"""

import os
import sys
import subprocess
from pathlib import Path

def build_minimal():
    """构建最小测试程序"""
    
    cmd = [
        sys.executable, "-m", "nuitka",
        
        # 基本配置
        "--module httpx",
        "-include-package-data=httpx"
        "--output-dir=precompiled_libs",

    ]
    
    print("开始构建最小测试程序...")
    
    try:
        result = subprocess.run(cmd, check=True)
        print("✅ 构建成功!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 最小化 httpx 测试构建")
    print("=" * 40)
    
    
    success = build_minimal()
    
    if success:
        print("\n🎉 构建完成!")
    else:
        print("\n💥 构建失败!")
        sys.exit(1)

if __name__ == "__main__":
    main()
