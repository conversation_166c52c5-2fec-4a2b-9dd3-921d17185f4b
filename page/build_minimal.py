#!/usr/bin/env python3
"""
最小化构建脚本，专门用于测试 httpx 问题
"""

import os
import sys
import subprocess
from pathlib import Path

def build_minimal():
    """构建最小测试程序"""
    
    cmd = [
        sys.executable, "-m", "nuitka",
        
        # 基本配置
        "--main=minimal_test.py",
        "--output-filename=minimal_test.exe",
        "--output-dir=dist",
        
        # 编译模式
        "--standalone",
        "--assume-yes-for-downloads",
        
        # 强制包含 httpx
        "--nofollow-import-to=httpx",
        # "--include-package=httpx",
        # "--include-module=httpx",
        
        # Windows 配置
        "--windows-console-mode=attach",
        
        # 简化输出
        "--show-progress",
    ]
    
    print("开始构建最小测试程序...")
    
    try:
        result = subprocess.run(cmd, check=True)
        print("✅ 构建成功!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 最小化 httpx 测试构建")
    print("=" * 40)
    
    if not Path("minimal_test.py").exists():
        print("❌ 错误: 找不到 minimal_test.py")
        sys.exit(1)
    
    success = build_minimal()
    
    if success:
        print("\n🎉 构建完成!")
        print("运行: dist\\minimal_test.exe")
    else:
        print("\n💥 构建失败!")
        sys.exit(1)

if __name__ == "__main__":
    main()
